<Window x:Class="MinecraftPvPHack.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MinecraftPvPHack"
        mc:Ignorable="d"
        Title="Minecraft PvP Hack v1.0" Height="600" Width="900"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ResizeMode="CanResize" WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Cores do tema -->
        <SolidColorBrush x:Key="PrimaryColor" Color="#FF1E1E1E"/>
        <SolidColorBrush x:Key="SecondaryColor" Color="#FF2D2D30"/>
        <SolidColorBrush x:Key="AccentColor" Color="#FF007ACC"/>
        <SolidColorBrush x:Key="TextColor" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="BorderColor" Color="#FF3F3F46"/>
        <SolidColorBrush x:Key="HoverColor" Color="#FF3E3E42"/>
        <SolidColorBrush x:Key="SuccessColor" Color="#FF4CAF50"/>
        <SolidColorBrush x:Key="DangerColor" Color="#FFF44336"/>

        <!-- Estilo para botões modernos -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="10,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="{StaticResource AccentColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Estilo para ToggleButton (módulos) -->
        <Style x:Key="ModuleToggle" TargetType="ToggleButton">
            <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15,8"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ToggleButton">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="3">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                            </Trigger>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter Property="Background" Value="{StaticResource SuccessColor}"/>
                                <Setter Property="BorderBrush" Value="{StaticResource SuccessColor}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Estilo para Sliders -->
        <Style x:Key="ModernSlider" TargetType="Slider">
            <Setter Property="Foreground" Value="{StaticResource AccentColor}"/>
            <Setter Property="Background" Value="{StaticResource BorderColor}"/>
            <Setter Property="Height" Value="20"/>
            <Setter Property="Margin" Value="5"/>
        </Style>

        <!-- Estilo para TextBox -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource SecondaryColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextColor}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8,5"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </Window.Resources>

    <!-- Borda principal com sombra -->
    <Border Background="{StaticResource PrimaryColor}"
            BorderBrush="{StaticResource BorderColor}"
            BorderThickness="1"
            CornerRadius="8">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.5"/>
        </Border.Effect>

        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="40"/> <!-- Header -->
                <RowDefinition Height="*"/>  <!-- Content -->
            </Grid.RowDefinitions>

            <!-- Header com título e controles da janela -->
            <Border Grid.Row="0" Background="{StaticResource SecondaryColor}"
                    CornerRadius="8,8,0,0" MouseLeftButtonDown="Header_MouseLeftButtonDown">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center" Margin="15,0">
                        <TextBlock Text="🎮" FontSize="16" Margin="0,0,10,0"/>
                        <TextBlock Text="Minecraft PvP Hack v1.0"
                                   Foreground="{StaticResource TextColor}"
                                   FontSize="14" FontWeight="Medium"
                                   VerticalAlignment="Center"/>
                    </StackPanel>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <Button Content="−" Width="40" Height="30"
                                Style="{StaticResource ModernButton}"
                                Click="MinimizeButton_Click" Margin="0"/>
                        <Button Content="✕" Width="40" Height="30"
                                Style="{StaticResource ModernButton}"
                                Click="CloseButton_Click" Margin="0"
                                Background="{StaticResource DangerColor}"/>
                    </StackPanel>
                </Grid>
            </Border>

            <!-- Conteúdo principal -->
            <Grid Grid.Row="1" Margin="10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="250"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="250"/>
                </Grid.ColumnDefinitions>

                <!-- Painel esquerdo - Módulos de Combat -->
                <Border Grid.Column="0" Background="{StaticResource SecondaryColor}"
                        BorderBrush="{StaticResource BorderColor}" BorderThickness="1"
                        CornerRadius="5" Margin="0,0,5,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="10">
                            <TextBlock Text="⚔️ COMBAT" Foreground="{StaticResource AccentColor}"
                                       FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>

                            <ToggleButton x:Name="KillAuraToggle" Content="KillAura"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <StackPanel x:Name="KillAuraSettings" Visibility="Collapsed" Margin="10,0,0,10">
                                <TextBlock Text="Range:" Foreground="{StaticResource TextColor}" FontSize="10"/>
                                <Slider x:Name="KillAuraRange" Style="{StaticResource ModernSlider}"
                                        Minimum="3" Maximum="6" Value="4.2" TickFrequency="0.1"/>
                                <TextBlock Text="{Binding ElementName=KillAuraRange, Path=Value, StringFormat='{}{0:F1}'}"
                                           Foreground="{StaticResource TextColor}" FontSize="10" HorizontalAlignment="Center"/>

                                <TextBlock Text="APS:" Foreground="{StaticResource TextColor}" FontSize="10" Margin="0,5,0,0"/>
                                <Slider x:Name="KillAuraAPS" Style="{StaticResource ModernSlider}"
                                        Minimum="1" Maximum="20" Value="12" TickFrequency="1"/>
                                <TextBlock Text="{Binding ElementName=KillAuraAPS, Path=Value, StringFormat='{}{0:F0}'}"
                                           Foreground="{StaticResource TextColor}" FontSize="10" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <ToggleButton x:Name="VelocityToggle" Content="Velocity"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <StackPanel x:Name="VelocitySettings" Visibility="Collapsed" Margin="10,0,0,10">
                                <TextBlock Text="Horizontal:" Foreground="{StaticResource TextColor}" FontSize="10"/>
                                <Slider x:Name="VelocityH" Style="{StaticResource ModernSlider}"
                                        Minimum="0" Maximum="100" Value="90"/>
                                <TextBlock Text="{Binding ElementName=VelocityH, Path=Value, StringFormat='{}{0:F0}%'}"
                                           Foreground="{StaticResource TextColor}" FontSize="10" HorizontalAlignment="Center"/>

                                <TextBlock Text="Vertical:" Foreground="{StaticResource TextColor}" FontSize="10" Margin="0,5,0,0"/>
                                <Slider x:Name="VelocityV" Style="{StaticResource ModernSlider}"
                                        Minimum="0" Maximum="100" Value="100"/>
                                <TextBlock Text="{Binding ElementName=VelocityV, Path=Value, StringFormat='{}{0:F0}%'}"
                                           Foreground="{StaticResource TextColor}" FontSize="10" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <ToggleButton x:Name="CriticalsToggle" Content="Criticals"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <ToggleButton x:Name="AutoClickerToggle" Content="AutoClicker"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <StackPanel x:Name="AutoClickerSettings" Visibility="Collapsed" Margin="10,0,0,10">
                                <TextBlock Text="CPS:" Foreground="{StaticResource TextColor}" FontSize="10"/>
                                <Slider x:Name="AutoClickerCPS" Style="{StaticResource ModernSlider}"
                                        Minimum="1" Maximum="20" Value="14"/>
                                <TextBlock Text="{Binding ElementName=AutoClickerCPS, Path=Value, StringFormat='{}{0:F0}'}"
                                           Foreground="{StaticResource TextColor}" FontSize="10" HorizontalAlignment="Center"/>
                            </StackPanel>

                            <ToggleButton x:Name="ReachToggle" Content="Reach"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <StackPanel x:Name="ReachSettings" Visibility="Collapsed" Margin="10,0,0,10">
                                <TextBlock Text="Distance:" Foreground="{StaticResource TextColor}" FontSize="10"/>
                                <Slider x:Name="ReachDistance" Style="{StaticResource ModernSlider}"
                                        Minimum="3" Maximum="6" Value="3.8"/>
                                <TextBlock Text="{Binding ElementName=ReachDistance, Path=Value, StringFormat='{}{0:F1}'}"
                                           Foreground="{StaticResource TextColor}" FontSize="10" HorizontalAlignment="Center"/>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Border>

                <!-- Painel central - Status e Informações -->
                <Border Grid.Column="1" Background="{StaticResource SecondaryColor}"
                        BorderBrush="{StaticResource BorderColor}" BorderThickness="1"
                        CornerRadius="5" Margin="5,0">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Status do jogo -->
                        <StackPanel Grid.Row="0" Margin="15">
                            <TextBlock Text="📊 STATUS" Foreground="{StaticResource AccentColor}"
                                       FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0">
                                    <TextBlock Text="Minecraft Status:" Foreground="{StaticResource TextColor}" FontSize="12"/>
                                    <TextBlock x:Name="MinecraftStatus" Text="🔴 Não Detectado"
                                               Foreground="{StaticResource DangerColor}" FontSize="11" Margin="0,2,0,10"/>

                                    <TextBlock Text="Módulos Ativos:" Foreground="{StaticResource TextColor}" FontSize="12"/>
                                    <TextBlock x:Name="ActiveModules" Text="0"
                                               Foreground="{StaticResource SuccessColor}" FontSize="11" Margin="0,2,0,0"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1">
                                    <TextBlock Text="FPS:" Foreground="{StaticResource TextColor}" FontSize="12"/>
                                    <TextBlock x:Name="FPSCounter" Text="--"
                                               Foreground="{StaticResource TextColor}" FontSize="11" Margin="0,2,0,10"/>

                                    <TextBlock Text="Ping:" Foreground="{StaticResource TextColor}" FontSize="12"/>
                                    <TextBlock x:Name="PingCounter" Text="--ms"
                                               Foreground="{StaticResource TextColor}" FontSize="11" Margin="0,2,0,0"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>

                        <!-- Log de atividades -->
                        <Border Grid.Row="1" Background="{StaticResource PrimaryColor}"
                                BorderBrush="{StaticResource BorderColor}" BorderThickness="1"
                                CornerRadius="3" Margin="15,0">
                            <ScrollViewer x:Name="LogScrollViewer" VerticalScrollBarVisibility="Auto">
                                <TextBlock x:Name="LogTextBlock"
                                           Foreground="{StaticResource TextColor}"
                                           FontFamily="Consolas" FontSize="10"
                                           Margin="10" TextWrapping="Wrap"
                                           Text="[INFO] Hack iniciado&#x0a;[INFO] Aguardando Minecraft...&#x0a;[INFO] Interface carregada com sucesso"/>
                            </ScrollViewer>
                        </Border>

                        <!-- Controles principais -->
                        <StackPanel Grid.Row="2" Orientation="Horizontal"
                                    HorizontalAlignment="Center" Margin="15">
                            <Button x:Name="InjectButton" Content="🚀 INJETAR"
                                    Style="{StaticResource ModernButton}"
                                    Background="{StaticResource SuccessColor}"
                                    Width="100" Click="InjectButton_Click"/>
                            <Button x:Name="EjectButton" Content="🛑 EJETAR"
                                    Style="{StaticResource ModernButton}"
                                    Background="{StaticResource DangerColor}"
                                    Width="100" Click="EjectButton_Click"/>
                            <Button x:Name="SaveConfigButton" Content="💾 SALVAR"
                                    Style="{StaticResource ModernButton}"
                                    Width="100" Click="SaveConfigButton_Click"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Painel direito - Módulos Visuais e Configurações -->
                <Border Grid.Column="2" Background="{StaticResource SecondaryColor}"
                        BorderBrush="{StaticResource BorderColor}" BorderThickness="1"
                        CornerRadius="5" Margin="5,0,0,0">
                    <ScrollViewer VerticalScrollBarVisibility="Auto">
                        <StackPanel Margin="10">
                            <TextBlock Text="👁️ VISUAL" Foreground="{StaticResource AccentColor}"
                                       FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>

                            <ToggleButton x:Name="ESPToggle" Content="ESP"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <ToggleButton x:Name="FullbrightToggle" Content="Fullbright"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <ToggleButton x:Name="XRayToggle" Content="X-Ray"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <ToggleButton x:Name="NoFallToggle" Content="NoFall"
                                          Style="{StaticResource ModuleToggle}"
                                          Click="ModuleToggle_Click"/>

                            <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>

                            <TextBlock Text="⚙️ CONFIGURAÇÕES" Foreground="{StaticResource AccentColor}"
                                       FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>

                            <TextBlock Text="Keybind Global:" Foreground="{StaticResource TextColor}" FontSize="12"/>
                            <TextBox x:Name="GlobalKeybind" Style="{StaticResource ModernTextBox}"
                                     Text="INSERT" IsReadOnly="True"
                                     PreviewKeyDown="Keybind_PreviewKeyDown"/>

                            <TextBlock Text="Tema:" Foreground="{StaticResource TextColor}" FontSize="12" Margin="0,10,0,0"/>
                            <ComboBox x:Name="ThemeComboBox"
                                      Background="{StaticResource SecondaryColor}"
                                      Foreground="{StaticResource TextColor}"
                                      BorderBrush="{StaticResource BorderColor}"
                                      Margin="5">
                                <ComboBoxItem Content="Dark Blue" IsSelected="True"/>
                                <ComboBoxItem Content="Dark Red"/>
                                <ComboBoxItem Content="Dark Green"/>
                                <ComboBoxItem Content="Purple"/>
                            </ComboBox>

                            <CheckBox x:Name="AutoInjectCheckBox" Content="Auto-Inject"
                                      Foreground="{StaticResource TextColor}"
                                      Margin="5,10,5,5"/>

                            <CheckBox x:Name="MinimizeToTrayCheckBox" Content="Minimizar para bandeja"
                                      Foreground="{StaticResource TextColor}"
                                      Margin="5"/>

                            <Separator Background="{StaticResource BorderColor}" Margin="0,15"/>

                            <TextBlock Text="📁 PROFILES" Foreground="{StaticResource AccentColor}"
                                       FontSize="14" FontWeight="Bold" Margin="0,0,0,10"/>

                            <ComboBox x:Name="ProfileComboBox"
                                      Background="{StaticResource SecondaryColor}"
                                      Foreground="{StaticResource TextColor}"
                                      BorderBrush="{StaticResource BorderColor}"
                                      Margin="5">
                                <ComboBoxItem Content="Default" IsSelected="True"/>
                                <ComboBoxItem Content="PvP Aggressive"/>
                                <ComboBoxItem Content="Legit"/>
                            </ComboBox>

                            <StackPanel Orientation="Horizontal" Margin="5">
                                <Button Content="💾" Style="{StaticResource ModernButton}"
                                        Width="30" ToolTip="Salvar Profile"/>
                                <Button Content="📁" Style="{StaticResource ModernButton}"
                                        Width="30" ToolTip="Carregar Profile"/>
                                <Button Content="🗑️" Style="{StaticResource ModernButton}"
                                        Width="30" ToolTip="Deletar Profile"/>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>
                </Border>
            </Grid>
        </Grid>
    </Border>
</Window>
