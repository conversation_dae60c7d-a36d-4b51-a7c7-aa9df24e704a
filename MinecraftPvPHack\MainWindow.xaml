<Window x:Class="MinecraftPvPHack.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MinecraftPvPHack"
        mc:Ignorable="d"
        Title="Minecraft PvP Hack" Height="700" Width="1000"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ResizeMode="CanResize" WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Cores minimalistas -->
        <SolidColorBrush x:Key="BackgroundColor" Color="#FF1A1A1A"/>
        <SolidColorBrush x:Key="SidebarColor" Color="#FF0F0F0F"/>
        <SolidColorBrush x:Key="CardColor" Color="#FF2A2A2A"/>
        <SolidColorBrush x:Key="AccentColor" Color="#FF4A9EFF"/>
        <SolidColorBrush x:Key="TextPrimary" Color="#FFFFFF"/>
        <SolidColorBrush x:Key="TextSecondary" Color="#FFAAAAAA"/>
        <SolidColorBrush x:Key="TextMuted" Color="#FF666666"/>
        <SolidColorBrush x:Key="BorderColor" Color="#FF333333"/>
        <SolidColorBrush x:Key="HoverColor" Color="#FF3A3A3A"/>
        <SolidColorBrush x:Key="ActiveColor" Color="#FF4A9EFF"/>

        <!-- Estilo para botões da sidebar -->
        <Style x:Key="SidebarButton" TargetType="Button">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Foreground" Value="{StaticResource TextSecondary}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Width" Value="50"/>
            <Setter Property="Height" Value="50"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" CornerRadius="8">
                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                                <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Estilo para botões ativos da sidebar -->
        <Style x:Key="SidebarButtonActive" TargetType="Button" BasedOn="{StaticResource SidebarButton}">
            <Setter Property="Background" Value="{StaticResource ActiveColor}"/>
            <Setter Property="Foreground" Value="White"/>
        </Style>

        <!-- Estilo para módulos -->
        <Style x:Key="ModuleItem" TargetType="Border">
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Estilo para sliders minimalistas -->
        <Style x:Key="MinimalSlider" TargetType="Slider">
            <Setter Property="Height" Value="20"/>
            <Setter Property="Margin" Value="0,5"/>
            <Setter Property="Foreground" Value="{StaticResource AccentColor}"/>
            <Setter Property="Background" Value="{StaticResource BorderColor}"/>
        </Style>

        <!-- Estilo para TextBox minimalista -->
        <Style x:Key="MinimalTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="{StaticResource CardColor}"/>
            <Setter Property="Foreground" Value="{StaticResource TextPrimary}"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="FontSize" Value="12"/>
        </Style>
    </Window.Resources>

    <!-- Layout principal minimalista -->
    <Border Background="{StaticResource BackgroundColor}" CornerRadius="12">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="10" Opacity="0.3"/>
        </Border.Effect>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="70"/>  <!-- Sidebar -->
                <ColumnDefinition Width="*"/>   <!-- Content -->
            </Grid.ColumnDefinitions>

            <!-- Sidebar com ícones -->
            <Border Grid.Column="0" Background="{StaticResource SidebarColor}" CornerRadius="12,0,0,12">
                <StackPanel Margin="10,20">
                    <!-- Logo/Avatar -->
                    <Border Background="{StaticResource AccentColor}" Width="40" Height="40"
                            CornerRadius="20" Margin="0,0,0,30">
                        <TextBlock Text="S" FontSize="20" FontWeight="Bold"
                                   Foreground="White" HorizontalAlignment="Center"
                                   VerticalAlignment="Center"/>
                    </Border>

                    <!-- Botões da sidebar -->
                    <Button x:Name="CombatBtn" Content="⚔️" Style="{StaticResource SidebarButtonActive}"
                            Click="SidebarButton_Click" Tag="Combat"/>
                    <Button x:Name="MovementBtn" Content="🏃" Style="{StaticResource SidebarButton}"
                            Click="SidebarButton_Click" Tag="Movement"/>
                    <Button x:Name="VisualBtn" Content="👁️" Style="{StaticResource SidebarButton}"
                            Click="SidebarButton_Click" Tag="Visual"/>
                    <Button x:Name="WorldBtn" Content="🌍" Style="{StaticResource SidebarButton}"
                            Click="SidebarButton_Click" Tag="World"/>
                    <Button x:Name="PlayerBtn" Content="👤" Style="{StaticResource SidebarButton}"
                            Click="SidebarButton_Click" Tag="Player"/>
                    <Button x:Name="MiscBtn" Content="⚙️" Style="{StaticResource SidebarButton}"
                            Click="SidebarButton_Click" Tag="Misc"/>

                    <!-- Spacer -->
                    <Border Height="50"/>

                    <!-- Configurações -->
                    <Button Content="⚙️" Style="{StaticResource SidebarButton}"
                            Click="SettingsButton_Click"/>
                </StackPanel>
            </Border>

            <!-- Área de conteúdo principal -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>   <!-- Header -->
                    <RowDefinition Height="*"/>    <!-- Content -->
                </Grid.RowDefinitions>

                <!-- Header da seção -->
                <Border Grid.Row="0" Background="Transparent" Padding="30,20">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock x:Name="SectionTitle" Text="World > Local Player"
                                       Foreground="{StaticResource TextPrimary}"
                                       FontSize="16" FontWeight="Medium"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Button Content="−" Width="30" Height="30"
                                    Background="Transparent" Foreground="{StaticResource TextSecondary}"
                                    BorderThickness="0" Click="MinimizeButton_Click"/>
                            <Button Content="✕" Width="30" Height="30"
                                    Background="Transparent" Foreground="{StaticResource TextSecondary}"
                                    BorderThickness="0" Click="CloseButton_Click"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Conteúdo das seções -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="30,0,30,30">
                    <Grid x:Name="ContentGrid">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Seção Combat (padrão visível) -->
                        <StackPanel x:Name="CombatSection" Grid.Column="0">
                            <TextBlock Text="Conditions" Foreground="{StaticResource TextPrimary}"
                                       FontSize="14" FontWeight="Medium" Margin="0,0,0,15"/>

                            <!-- Lista de módulos -->
                            <Border Style="{StaticResource ModuleItem}" MouseLeftButtonDown="ModuleItem_Click" Tag="KillAura">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="KillAura" Foreground="{StaticResource TextPrimary}"
                                               FontSize="13" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="⌨️" Foreground="{StaticResource TextMuted}"
                                               FontSize="12" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>

                            <Border Style="{StaticResource ModuleItem}" MouseLeftButtonDown="ModuleItem_Click" Tag="Velocity">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Velocity" Foreground="{StaticResource TextPrimary}"
                                               FontSize="13" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="⌨️" Foreground="{StaticResource TextMuted}"
                                               FontSize="12" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>

                            <Border Style="{StaticResource ModuleItem}" MouseLeftButtonDown="ModuleItem_Click" Tag="AutoClicker">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="AutoClicker" Foreground="{StaticResource TextPrimary}"
                                               FontSize="13" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="⌨️" Foreground="{StaticResource TextMuted}"
                                               FontSize="12" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>

                            <Border Style="{StaticResource ModuleItem}" MouseLeftButtonDown="ModuleItem_Click" Tag="Reach">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Reach" Foreground="{StaticResource TextPrimary}"
                                               FontSize="13" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="⌨️" Foreground="{StaticResource TextMuted}"
                                               FontSize="12" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>

                            <Border Style="{StaticResource ModuleItem}" MouseLeftButtonDown="ModuleItem_Click" Tag="Criticals">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Criticals" Foreground="{StaticResource TextPrimary}"
                                               FontSize="13" VerticalAlignment="Center"/>
                                    <TextBlock Grid.Column="1" Text="⌨️" Foreground="{StaticResource TextMuted}"
                                               FontSize="12" VerticalAlignment="Center"/>
                                </Grid>
                            </Border>
                        </StackPanel>

                        <!-- Seção de Customização -->
                        <StackPanel x:Name="CustomizationSection" Grid.Column="2">
                            <TextBlock Text="Customization" Foreground="{StaticResource TextPrimary}"
                                       FontSize="14" FontWeight="Medium" Margin="0,0,0,15"/>

                            <!-- KillAura Range -->
                            <StackPanel Margin="0,0,0,20">
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="KillAura Range"
                                               Foreground="{StaticResource TextPrimary}" FontSize="12"/>
                                    <TextBlock Grid.Column="1" x:Name="KillAuraRangeValue" Text="4.20"
                                               Foreground="{StaticResource AccentColor}" FontSize="12"/>
                                </Grid>
                                <Slider x:Name="KillAuraRangeSlider" Style="{StaticResource MinimalSlider}"
                                        Minimum="3.0" Maximum="6.0" Value="4.2"
                                        ValueChanged="Slider_ValueChanged"/>
                            </StackPanel>

                            <!-- AutoClicker CPS -->
                            <StackPanel Margin="0,0,0,20">
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="AutoClicker CPS"
                                               Foreground="{StaticResource TextPrimary}" FontSize="12"/>
                                    <TextBlock Grid.Column="1" x:Name="AutoClickerCPSValue" Text="14"
                                               Foreground="{StaticResource AccentColor}" FontSize="12"/>
                                </Grid>
                                <Slider x:Name="AutoClickerCPSSlider" Style="{StaticResource MinimalSlider}"
                                        Minimum="1" Maximum="20" Value="14"
                                        ValueChanged="Slider_ValueChanged"/>
                            </StackPanel>

                            <!-- Velocity Horizontal -->
                            <StackPanel Margin="0,0,0,20">
                                <Grid Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Velocity Horizontal"
                                               Foreground="{StaticResource TextPrimary}" FontSize="12"/>
                                    <TextBlock Grid.Column="1" x:Name="VelocityHValue" Text="90%"
                                               Foreground="{StaticResource AccentColor}" FontSize="12"/>
                                </Grid>
                                <Slider x:Name="VelocityHSlider" Style="{StaticResource MinimalSlider}"
                                        Minimum="0" Maximum="100" Value="90"
                                        ValueChanged="Slider_ValueChanged"/>
                            </StackPanel>

                            <!-- Status -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="8" Padding="15" Margin="0,20,0,0">
                                <StackPanel>
                                    <TextBlock Text="Status" Foreground="{StaticResource TextPrimary}"
                                               FontSize="12" FontWeight="Medium" Margin="0,0,0,10"/>
                                    <Grid>
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Minecraft"
                                                   Foreground="{StaticResource TextSecondary}" FontSize="11"/>
                                        <TextBlock Grid.Column="1" x:Name="MinecraftStatusText" Text="🟢 Connected"
                                                   Foreground="{StaticResource AccentColor}" FontSize="11"/>
                                    </Grid>
                                    <Grid Margin="0,5,0,0">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Active Modules"
                                                   Foreground="{StaticResource TextSecondary}" FontSize="11"/>
                                        <TextBlock Grid.Column="1" x:Name="ActiveModulesText" Text="3"
                                                   Foreground="{StaticResource AccentColor}" FontSize="11"/>
                                    </Grid>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Border>
</Window>


