<Window x:Class="MinecraftPvPHack.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MinecraftPvPHack"
        mc:Ignorable="d"
        Title="Minecraft PvP Hack" Height="700" Width="1000"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ResizeMode="CanResize" WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Cores profissionais com gradientes -->
        <SolidColorBrush x:Key="BackgroundColor" Color="#FF0D1117"/>
        <SolidColorBrush x:Key="SidebarColor" Color="#FF161B22"/>
        <SolidColorBrush x:Key="CardColor" Color="#FF21262D"/>
        <SolidColorBrush x:Key="AccentColor" Color="#FF238636"/>
        <SolidColorBrush x:Key="AccentRed" Color="#FFD73A49"/>
        <SolidColorBrush x:Key="AccentBlue" Color="#FF0366D6"/>
        <SolidColorBrush x:Key="AccentPurple" Color="#FF6F42C1"/>
        <SolidColorBrush x:Key="TextPrimary" Color="#FFF0F6FC"/>
        <SolidColorBrush x:Key="TextSecondary" Color="#FF8B949E"/>
        <SolidColorBrush x:Key="TextMuted" Color="#FF6E7681"/>
        <SolidColorBrush x:Key="BorderColor" Color="#FF30363D"/>
        <SolidColorBrush x:Key="HoverColor" Color="#FF262C36"/>

        <!-- Gradientes profissionais -->
        <LinearGradientBrush x:Key="GlowGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF238636" Offset="0"/>
            <GradientStop Color="#FF2EA043" Offset="1"/>
        </LinearGradientBrush>

        <LinearGradientBrush x:Key="RedGlowGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FFD73A49" Offset="0"/>
            <GradientStop Color="#FFDA3633" Offset="1"/>
        </LinearGradientBrush>

        <RadialGradientBrush x:Key="GlowEffect" Center="0.5,0.5" RadiusX="1" RadiusY="1">
            <GradientStop Color="#33238636" Offset="0"/>
            <GradientStop Color="#00238636" Offset="1"/>
        </RadialGradientBrush>

        <!-- Estilo profissional para ícones da sidebar -->
        <Style x:Key="SidebarIcon" TargetType="Border">
            <Setter Property="Width" Value="45"/>
            <Setter Property="Height" Value="45"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="Transparent"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Transparent" Direction="270" ShadowDepth="0" BlurRadius="0"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                    <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#FF238636" Direction="270" ShadowDepth="0" BlurRadius="15" Opacity="0.6"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Estilo para ícone ativo com glow -->
        <Style x:Key="SidebarIconActive" TargetType="Border" BasedOn="{StaticResource SidebarIcon}">
            <Setter Property="Background" Value="{StaticResource GlowGradient}"/>
            <Setter Property="BorderBrush" Value="{StaticResource AccentColor}"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#FF238636" Direction="270" ShadowDepth="0" BlurRadius="20" Opacity="0.8"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Toggle Switch profissional -->
        <Style x:Key="ProfessionalToggle" TargetType="Border">
            <Setter Property="Width" Value="44"/>
            <Setter Property="Height" Value="24"/>
            <Setter Property="CornerRadius" Value="12"/>
            <Setter Property="Background" Value="{StaticResource BorderColor}"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsEnabled}" Value="True">
                    <Setter Property="Background" Value="{StaticResource GlowGradient}"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#FF238636" Direction="270" ShadowDepth="0" BlurRadius="12" Opacity="0.7"/>
                        </Setter.Value>
                    </Setter>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- Slider profissional com glow -->
        <Style x:Key="ProfessionalSlider" TargetType="Slider">
            <Setter Property="Height" Value="6"/>
            <Setter Property="Margin" Value="0,8"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Slider">
                        <Grid>
                            <Border x:Name="TrackBackground"
                                    Height="6"
                                    Background="{StaticResource BorderColor}"
                                    CornerRadius="3"/>
                            <Border x:Name="PART_SelectionRange"
                                    Height="6"
                                    Background="{StaticResource GlowGradient}"
                                    CornerRadius="3"
                                    HorizontalAlignment="Left"
                                    Width="100">
                                <Border.Effect>
                                    <DropShadowEffect Color="#FF238636" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.6"/>
                                </Border.Effect>
                            </Border>
                            <Thumb x:Name="PART_Track"
                                   Width="18"
                                   Height="18"
                                   Background="White"
                                   BorderBrush="{StaticResource AccentColor}"
                                   BorderThickness="2"
                                   Template="{DynamicResource SliderThumbTemplate}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Template para thumb do slider -->
        <ControlTemplate x:Key="SliderThumbTemplate" TargetType="Thumb">
            <Border Background="{TemplateBinding Background}"
                    BorderBrush="{TemplateBinding BorderBrush}"
                    BorderThickness="{TemplateBinding BorderThickness}"
                    CornerRadius="9">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                </Border.Effect>
            </Border>
        </ControlTemplate>
    </Window.Resources>

    <!-- Layout principal ultra-profissional -->
    <Border Background="{StaticResource BackgroundColor}" CornerRadius="16">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="315" ShadowDepth="15" BlurRadius="25" Opacity="0.4"/>
        </Border.Effect>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="80"/>  <!-- Sidebar -->
                <ColumnDefinition Width="*"/>   <!-- Content -->
            </Grid.ColumnDefinitions>

            <!-- Sidebar ultra-profissional -->
            <Border Grid.Column="0" Background="{StaticResource SidebarColor}" CornerRadius="16,0,0,16">
                <Border.Effect>
                    <DropShadowEffect Color="Black" Direction="0" ShadowDepth="5" BlurRadius="10" Opacity="0.2"/>
                </Border.Effect>

                <StackPanel Margin="17,25">
                    <!-- Logo profissional com glow -->
                    <Border Width="46" Height="46" CornerRadius="12" Margin="0,0,0,35">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#FF238636" Offset="0"/>
                                <GradientStop Color="#FF2EA043" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <Border.Effect>
                            <DropShadowEffect Color="#FF238636" Direction="270" ShadowDepth="0" BlurRadius="20" Opacity="0.8"/>
                        </Border.Effect>
                        <TextBlock Text="TF" FontSize="16" FontWeight="Bold"
                                   Foreground="White" HorizontalAlignment="Center"
                                   VerticalAlignment="Center" FontFamily="Segoe UI"/>
                    </Border>

                    <!-- Ícones da sidebar com efeitos -->
                    <Border x:Name="CombatIcon" Style="{StaticResource SidebarIconActive}"
                            MouseLeftButtonDown="SidebarIcon_Click" Tag="Combat">
                        <Path Data="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21A7,7 0 0,1 14,26H13V21.27C13.6,21.61 14,22.26 14,23A2,2 0 0,1 12,25A2,2 0 0,1 10,23C10,22.26 10.4,21.61 11,21.27V7.73C10.4,7.39 10,6.74 10,6A2,2 0 0,1 12,4A2,2 0 0,1 12,2Z"
                              Fill="{StaticResource TextPrimary}" Width="20" Height="20"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <Border x:Name="MovementIcon" Style="{StaticResource SidebarIcon}"
                            MouseLeftButtonDown="SidebarIcon_Click" Tag="Movement">
                        <Path Data="M13.5,5.5C14.59,5.5 15.5,4.59 15.5,3.5S14.59,1.5 13.5,1.5 11.5,2.41 11.5,3.5 12.41,5.5 13.5,5.5M9.89,19.38L10.89,15L13,17V23H15V15.5L12.89,13.5L13.5,10.5C14.79,12 16.79,13 19,13V11C17.09,11 15.5,10 14.69,8.58L13.69,7C13.29,6.38 12.69,6 12,6C11.69,6 11.5,6.08 11.19,6.08L6,8.28V12H8V9.58L9.79,8.88L8.19,17L3.29,16L2.89,18L9.89,19.38Z"
                              Fill="{StaticResource TextSecondary}" Width="20" Height="20"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <Border x:Name="VisualIcon" Style="{StaticResource SidebarIcon}"
                            MouseLeftButtonDown="SidebarIcon_Click" Tag="Visual">
                        <Path Data="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"
                              Fill="{StaticResource TextSecondary}" Width="20" Height="20"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <Border x:Name="WorldIcon" Style="{StaticResource SidebarIcon}"
                            MouseLeftButtonDown="SidebarIcon_Click" Tag="World">
                        <Path Data="M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"
                              Fill="{StaticResource TextSecondary}" Width="20" Height="20"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <Border x:Name="PlayerIcon" Style="{StaticResource SidebarIcon}"
                            MouseLeftButtonDown="SidebarIcon_Click" Tag="Player">
                        <Path Data="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
                              Fill="{StaticResource TextSecondary}" Width="20" Height="20"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>

                    <Border x:Name="MiscIcon" Style="{StaticResource SidebarIcon}"
                            MouseLeftButtonDown="SidebarIcon_Click" Tag="Misc">
                        <Path Data="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"
                              Fill="{StaticResource TextSecondary}" Width="20" Height="20"
                              HorizontalAlignment="Center" VerticalAlignment="Center"/>
                    </Border>
                </StackPanel>
            </Border>

            <!-- Área de conteúdo ultra-profissional -->
            <Grid Grid.Column="1" Background="{StaticResource BackgroundColor}">
                <Grid.RowDefinitions>
                    <RowDefinition Height="70"/>   <!-- Header -->
                    <RowDefinition Height="*"/>    <!-- Content -->
                </Grid.RowDefinitions>

                <!-- Header profissional com gradiente -->
                <Border Grid.Row="0" Padding="35,20">
                    <Border.Background>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                            <GradientStop Color="#FF161B22" Offset="0"/>
                            <GradientStop Color="#FF0D1117" Offset="1"/>
                        </LinearGradientBrush>
                    </Border.Background>

                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                            <TextBlock x:Name="SectionTitle" Text="SILENT"
                                       Foreground="{StaticResource TextPrimary}"
                                       FontSize="24" FontWeight="Bold" FontFamily="Segoe UI"/>
                            <Border Width="8" Height="8" CornerRadius="4" Background="{StaticResource AccentColor}"
                                    Margin="15,0,0,0" VerticalAlignment="Center">
                                <Border.Effect>
                                    <DropShadowEffect Color="#FF238636" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.8"/>
                                </Border.Effect>
                            </Border>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <Border Width="32" Height="32" CornerRadius="6" Background="{StaticResource HoverColor}"
                                    Margin="5,0" Cursor="Hand" MouseLeftButtonDown="MinimizeButton_Click">
                                <Path Data="M19,13H5V11H19V13Z" Fill="{StaticResource TextSecondary}"
                                      Width="16" Height="16" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                            <Border Width="32" Height="32" CornerRadius="6" Background="{StaticResource HoverColor}"
                                    Margin="5,0" Cursor="Hand" MouseLeftButtonDown="CloseButton_Click">
                                <Path Data="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"
                                      Fill="{StaticResource TextSecondary}" Width="16" Height="16"
                                      HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </Border>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Conteúdo principal profissional -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Hidden" Padding="35,20,35,35">
                    <Grid x:Name="ContentGrid">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="40"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- Seção de módulos profissional -->
                        <StackPanel x:Name="CombatSection" Grid.Column="0">
                            <!-- Módulo Enable Silent -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Enable Silent"
                                               Foreground="{StaticResource TextPrimary}"
                                               FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" x:Name="EnableSilentToggle" Style="{StaticResource ProfessionalToggle}"
                                            MouseLeftButtonDown="Toggle_Click" Tag="EnableSilent">
                                        <Ellipse x:Name="EnableSilentThumb" Width="18" Height="18" Fill="White"
                                                 HorizontalAlignment="Left" Margin="3,0,0,0">
                                            <Ellipse.Effect>
                                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                                            </Ellipse.Effect>
                                        </Ellipse>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Módulo Silent Key -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Silent Key"
                                               Foreground="{StaticResource TextMuted}"
                                               FontSize="14" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" Background="{StaticResource BorderColor}" CornerRadius="6"
                                            Padding="8,4" Cursor="Hand">
                                        <Path Data="M12,17C10.89,17 10,16.1 10,15C10,13.89 10.89,13 12,13A2,2 0 0,1 14,15A2,2 0 0,1 12,17M18,20V10H6V20H18M18,8A2,2 0 0,1 20,10V20A2,2 0 0,1 18,22H6C4.89,22 4,21.1 4,20V10C4,8.89 4.89,8 6,8H7V6A5,5 0 0,1 12,1A5,5 0 0,1 17,6V8H18M12,3A3,3 0 0,0 9,6V8H15V6A3,3 0 0,0 12,3Z"
                                              Fill="{StaticResource TextMuted}" Width="14" Height="14"/>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Módulo Undetectable Silent -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Undetectable Silent"
                                               Foreground="{StaticResource TextPrimary}"
                                               FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" x:Name="UndetectableToggle" Style="{StaticResource ProfessionalToggle}"
                                            MouseLeftButtonDown="Toggle_Click" Tag="Undetectable">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                <GradientStop Color="#FFD73A49" Offset="0"/>
                                                <GradientStop Color="#FFDA3633" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>
                                        <Border.Effect>
                                            <DropShadowEffect Color="#FFD73A49" Direction="270" ShadowDepth="0" BlurRadius="12" Opacity="0.7"/>
                                        </Border.Effect>
                                        <Ellipse x:Name="UndetectableThumb" Width="18" Height="18" Fill="White"
                                                 HorizontalAlignment="Right" Margin="0,0,3,0">
                                            <Ellipse.Effect>
                                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                                            </Ellipse.Effect>
                                        </Ellipse>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Módulo Visible Check -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Visible Check"
                                               Foreground="{StaticResource TextPrimary}"
                                               FontSize="14" FontWeight="Medium" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" x:Name="VisibleCheckToggle" Style="{StaticResource ProfessionalToggle}"
                                            MouseLeftButtonDown="Toggle_Click" Tag="VisibleCheck">
                                        <Border.Background>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                <GradientStop Color="#FFD73A49" Offset="0"/>
                                                <GradientStop Color="#FFDA3633" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Border.Background>
                                        <Border.Effect>
                                            <DropShadowEffect Color="#FFD73A49" Direction="270" ShadowDepth="0" BlurRadius="12" Opacity="0.7"/>
                                        </Border.Effect>
                                        <Ellipse x:Name="VisibleCheckThumb" Width="18" Height="18" Fill="White"
                                                 HorizontalAlignment="Right" Margin="0,0,3,0">
                                            <Ellipse.Effect>
                                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                                            </Ellipse.Effect>
                                        </Ellipse>
                                    </Border>
                                </Grid>
                            </Border>
                        </StackPanel>

                        <!-- Seção AIMBOT profissional -->
                        <StackPanel x:Name="AimbotSection" Grid.Column="2">
                            <TextBlock Text="AIMBOT" Foreground="{StaticResource TextPrimary}"
                                       FontSize="18" FontWeight="Bold" Margin="0,0,0,25"/>

                            <!-- Enable Aimbot -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Enable Aimbot"
                                               Foreground="{StaticResource TextMuted}"
                                               FontSize="14" VerticalAlignment="Center"/>
                                    <Border Grid.Column="1" x:Name="EnableAimbotToggle" Style="{StaticResource ProfessionalToggle}"
                                            MouseLeftButtonDown="Toggle_Click" Tag="EnableAimbot">
                                        <Ellipse x:Name="EnableAimbotThumb" Width="18" Height="18" Fill="White"
                                                 HorizontalAlignment="Left" Margin="3,0,0,0">
                                            <Ellipse.Effect>
                                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.3"/>
                                            </Ellipse.Effect>
                                        </Ellipse>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- FOV Slider -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <StackPanel>
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="FOV"
                                                   Foreground="{StaticResource TextPrimary}" FontSize="14" FontWeight="Medium"/>
                                        <Border Grid.Column="1" Background="{StaticResource AccentRed}" CornerRadius="8"
                                                Padding="8,4" MinWidth="30">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#FFD73A49" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.6"/>
                                            </Border.Effect>
                                            <TextBlock x:Name="FOVValue" Text="0" Foreground="White"
                                                       FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </Border>
                                    </Grid>
                                    <Slider x:Name="FOVSlider" Style="{StaticResource ProfessionalSlider}"
                                            Minimum="0" Maximum="180" Value="0"
                                            ValueChanged="Slider_ValueChanged"/>
                                </StackPanel>
                            </Border>

                            <!-- Distance Slider -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <StackPanel>
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Distance"
                                                   Foreground="{StaticResource TextPrimary}" FontSize="14" FontWeight="Medium"/>
                                        <Border Grid.Column="1" Background="{StaticResource AccentRed}" CornerRadius="8"
                                                Padding="8,4" MinWidth="40">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#FFD73A49" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.6"/>
                                            </Border.Effect>
                                            <TextBlock x:Name="DistanceValue" Text="100" Foreground="White"
                                                       FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </Border>
                                    </Grid>
                                    <Slider x:Name="DistanceSlider" Style="{StaticResource ProfessionalSlider}"
                                            Minimum="0" Maximum="200" Value="100"
                                            ValueChanged="Slider_ValueChanged"/>
                                </StackPanel>
                            </Border>

                            <!-- X Smooth Slider -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <StackPanel>
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="X Smooth"
                                                   Foreground="{StaticResource TextPrimary}" FontSize="14" FontWeight="Medium"/>
                                        <Border Grid.Column="1" Background="{StaticResource AccentRed}" CornerRadius="8"
                                                Padding="8,4" MinWidth="40">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#FFD73A49" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.6"/>
                                            </Border.Effect>
                                            <TextBlock x:Name="XSmoothValue" Text="0.00" Foreground="White"
                                                       FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </Border>
                                    </Grid>
                                    <Slider x:Name="XSmoothSlider" Style="{StaticResource ProfessionalSlider}"
                                            Minimum="0" Maximum="10" Value="0"
                                            ValueChanged="Slider_ValueChanged"/>
                                </StackPanel>
                            </Border>

                            <!-- Y Smooth Slider -->
                            <Border Background="{StaticResource CardColor}" CornerRadius="12" Padding="20,16" Margin="0,0,0,12">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="8" Opacity="0.1"/>
                                </Border.Effect>
                                <StackPanel>
                                    <Grid Margin="0,0,0,12">
                                        <Grid.ColumnDefinitions>
                                            <ColumnDefinition Width="*"/>
                                            <ColumnDefinition Width="Auto"/>
                                        </Grid.ColumnDefinitions>
                                        <TextBlock Grid.Column="0" Text="Y Smooth"
                                                   Foreground="{StaticResource TextPrimary}" FontSize="14" FontWeight="Medium"/>
                                        <Border Grid.Column="1" Background="{StaticResource AccentRed}" CornerRadius="8"
                                                Padding="8,4" MinWidth="40">
                                            <Border.Effect>
                                                <DropShadowEffect Color="#FFD73A49" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.6"/>
                                            </Border.Effect>
                                            <TextBlock x:Name="YSmoothValue" Text="0.00" Foreground="White"
                                                       FontSize="12" FontWeight="Bold" HorizontalAlignment="Center"/>
                                        </Border>
                                    </Grid>
                                    <Slider x:Name="YSmoothSlider" Style="{StaticResource ProfessionalSlider}"
                                            Minimum="0" Maximum="10" Value="0"
                                            ValueChanged="Slider_ValueChanged"/>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Border>
</Window>


