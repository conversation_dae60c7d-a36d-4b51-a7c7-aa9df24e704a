# 🎮 Minecraft PvP Hack - Interface Minimalista

Uma interface **minimalista e profissional** para hack PvP do Minecraft 1.8.9, desenvolvida em WPF com Visual Studio 2022. Design inspirado em ferramentas modernas de desenvolvimento.

## ✨ Características

### 🎨 Design Minimalista
- Interface dark theme ultra-moderna
- Sidebar com ícones intuitivos
- Layout limpo e organizado
- Cores suaves e contrastes perfeitos
- Animações sutis e responsivas

### 🎯 Interface Intuitiva
- **Sidebar com Ícones**: Navegação rápida entre categorias
- **Combat**: ⚔️ <PERSON>ó<PERSON><PERSON> de combate (KillAura, Velocity, AutoClicker, Reach, Criticals)
- **Movement**: 🏃 <PERSON><PERSON><PERSON><PERSON> de movimento (Speed, Fly, NoFall)
- **Visual**: 👁️ <PERSON><PERSON><PERSON><PERSON> visuais (ESP, Fullbright, X-Ray)
- **World**: 🌍 Interação com mundo (Nuker, FastBreak)
- **Player**: 👤 Modificações do player (Regen, FastEat)
- **Misc**: ⚙️ Utilitários diversos

### 🎛️ Customização em Tempo Real
- **Sliders Responsivos**: Ajuste preciso de valores
- **Status em Tempo Real**: Conexão com Minecraft e módulos ativos
- **Configurações Persistentes**: Valores salvos automaticamente
- **Interface Adaptável**: Responde a diferentes categorias

## 🚀 Como Usar

### 1. Compilar o Projeto
```bash
cd MinecraftPvPHack
dotnet build
```

### 2. Executar a Interface
```bash
dotnet run
```

### 3. Usar o Hack
1. Abra o Minecraft 1.8.9
2. Aguarde a detecção automática (status ficará verde)
3. Clique em "🚀 INJETAR" para ativar o hack
4. Configure os módulos conforme necessário
5. Use "💾 SALVAR" para salvar suas configurações

## 🔧 Estrutura do Projeto

```
MinecraftPvPHack/
├── MainWindow.xaml          # Interface visual (XAML)
├── MainWindow.xaml.cs       # Lógica da interface (C#)
├── App.xaml                 # Configurações da aplicação
├── App.xaml.cs              # Inicialização da aplicação
└── MinecraftPvPHack.csproj  # Arquivo do projeto
```

## 🎯 Próximos Passos para Integração

### 1. Implementar Injeção Real
- Adicionar biblioteca de injeção de DLL
- Implementar detecção do processo do Minecraft
- Criar sistema de hooks para as funções do jogo

### 2. Adicionar Funcionalidades dos Módulos
- Implementar lógica real do KillAura
- Adicionar sistema de packets para Velocity
- Criar overlay para ESP e outros visuais

### 3. Sistema de Configuração
- Salvar/carregar configurações em arquivo JSON
- Sistema de profiles mais avançado
- Backup automático de configurações

## 🛠️ Tecnologias Utilizadas

- **Framework**: .NET 9.0
- **UI**: WPF (Windows Presentation Foundation)
- **IDE**: Visual Studio 2022
- **Linguagem**: C#

## ⚠️ Aviso Legal

Este projeto é apenas para fins educacionais e de demonstração. O uso de hacks em servidores públicos pode resultar em banimento. Use apenas em servidores próprios ou com permissão.

## 🎨 Personalização

### Alterar Cores do Tema
Edite as cores no arquivo `MainWindow.xaml`:
```xml
<SolidColorBrush x:Key="PrimaryColor" Color="#FF1E1E1E"/>
<SolidColorBrush x:Key="AccentColor" Color="#FF007ACC"/>
```

### Adicionar Novos Módulos
1. Adicione o ToggleButton no XAML
2. Implemente o handler no código-behind
3. Adicione as configurações específicas do módulo

### Modificar Layout
O layout usa Grid com 3 colunas:
- Coluna 0: Módulos de Combat
- Coluna 1: Status e Log
- Coluna 2: Módulos Visuais e Configurações

## 📞 Suporte

Para dúvidas sobre implementação ou personalização, consulte a documentação do WPF ou entre em contato.

---
**Desenvolvido com ❤️ para a comunidade Minecraft PvP**
