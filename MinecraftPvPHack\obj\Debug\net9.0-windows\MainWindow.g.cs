﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "E144F6A8DD60997C386E70E1649DEF9777632805"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MinecraftPvPHack;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MinecraftPvPHack {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 171 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton KillAuraToggle;
        
        #line default
        #line hidden
        
        
        #line 175 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel KillAuraSettings;
        
        #line default
        #line hidden
        
        
        #line 177 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider KillAuraRange;
        
        #line default
        #line hidden
        
        
        #line 183 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider KillAuraAPS;
        
        #line default
        #line hidden
        
        
        #line 189 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton VelocityToggle;
        
        #line default
        #line hidden
        
        
        #line 193 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel VelocitySettings;
        
        #line default
        #line hidden
        
        
        #line 195 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VelocityH;
        
        #line default
        #line hidden
        
        
        #line 201 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider VelocityV;
        
        #line default
        #line hidden
        
        
        #line 207 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton CriticalsToggle;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton AutoClickerToggle;
        
        #line default
        #line hidden
        
        
        #line 215 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AutoClickerSettings;
        
        #line default
        #line hidden
        
        
        #line 217 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider AutoClickerCPS;
        
        #line default
        #line hidden
        
        
        #line 223 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ReachToggle;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel ReachSettings;
        
        #line default
        #line hidden
        
        
        #line 229 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider ReachDistance;
        
        #line default
        #line hidden
        
        
        #line 262 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock MinecraftStatus;
        
        #line default
        #line hidden
        
        
        #line 266 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ActiveModules;
        
        #line default
        #line hidden
        
        
        #line 272 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FPSCounter;
        
        #line default
        #line hidden
        
        
        #line 276 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock PingCounter;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ScrollViewer LogScrollViewer;
        
        #line default
        #line hidden
        
        
        #line 287 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock LogTextBlock;
        
        #line default
        #line hidden
        
        
        #line 298 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button InjectButton;
        
        #line default
        #line hidden
        
        
        #line 302 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button EjectButton;
        
        #line default
        #line hidden
        
        
        #line 306 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SaveConfigButton;
        
        #line default
        #line hidden
        
        
        #line 322 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton ESPToggle;
        
        #line default
        #line hidden
        
        
        #line 326 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton FullbrightToggle;
        
        #line default
        #line hidden
        
        
        #line 330 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton XRayToggle;
        
        #line default
        #line hidden
        
        
        #line 334 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Primitives.ToggleButton NoFallToggle;
        
        #line default
        #line hidden
        
        
        #line 344 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox GlobalKeybind;
        
        #line default
        #line hidden
        
        
        #line 349 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ThemeComboBox;
        
        #line default
        #line hidden
        
        
        #line 360 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox AutoInjectCheckBox;
        
        #line default
        #line hidden
        
        
        #line 364 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox MinimizeToTrayCheckBox;
        
        #line default
        #line hidden
        
        
        #line 373 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ComboBox ProfileComboBox;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MinecraftPvPHack;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 127 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Header_MouseLeftButtonDown);
            
            #line default
            #line hidden
            return;
            case 2:
            
            #line 145 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 148 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.KillAuraToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 173 "..\..\..\MainWindow.xaml"
            this.KillAuraToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.KillAuraSettings = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 6:
            this.KillAuraRange = ((System.Windows.Controls.Slider)(target));
            return;
            case 7:
            this.KillAuraAPS = ((System.Windows.Controls.Slider)(target));
            return;
            case 8:
            this.VelocityToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 191 "..\..\..\MainWindow.xaml"
            this.VelocityToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.VelocitySettings = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 10:
            this.VelocityH = ((System.Windows.Controls.Slider)(target));
            return;
            case 11:
            this.VelocityV = ((System.Windows.Controls.Slider)(target));
            return;
            case 12:
            this.CriticalsToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 209 "..\..\..\MainWindow.xaml"
            this.CriticalsToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.AutoClickerToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 213 "..\..\..\MainWindow.xaml"
            this.AutoClickerToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.AutoClickerSettings = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 15:
            this.AutoClickerCPS = ((System.Windows.Controls.Slider)(target));
            return;
            case 16:
            this.ReachToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 225 "..\..\..\MainWindow.xaml"
            this.ReachToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.ReachSettings = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 18:
            this.ReachDistance = ((System.Windows.Controls.Slider)(target));
            return;
            case 19:
            this.MinecraftStatus = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 20:
            this.ActiveModules = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 21:
            this.FPSCounter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.PingCounter = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 23:
            this.LogScrollViewer = ((System.Windows.Controls.ScrollViewer)(target));
            return;
            case 24:
            this.LogTextBlock = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            this.InjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 301 "..\..\..\MainWindow.xaml"
            this.InjectButton.Click += new System.Windows.RoutedEventHandler(this.InjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.EjectButton = ((System.Windows.Controls.Button)(target));
            
            #line 305 "..\..\..\MainWindow.xaml"
            this.EjectButton.Click += new System.Windows.RoutedEventHandler(this.EjectButton_Click);
            
            #line default
            #line hidden
            return;
            case 27:
            this.SaveConfigButton = ((System.Windows.Controls.Button)(target));
            
            #line 308 "..\..\..\MainWindow.xaml"
            this.SaveConfigButton.Click += new System.Windows.RoutedEventHandler(this.SaveConfigButton_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.ESPToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 324 "..\..\..\MainWindow.xaml"
            this.ESPToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 29:
            this.FullbrightToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 328 "..\..\..\MainWindow.xaml"
            this.FullbrightToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 30:
            this.XRayToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 332 "..\..\..\MainWindow.xaml"
            this.XRayToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 31:
            this.NoFallToggle = ((System.Windows.Controls.Primitives.ToggleButton)(target));
            
            #line 336 "..\..\..\MainWindow.xaml"
            this.NoFallToggle.Click += new System.Windows.RoutedEventHandler(this.ModuleToggle_Click);
            
            #line default
            #line hidden
            return;
            case 32:
            this.GlobalKeybind = ((System.Windows.Controls.TextBox)(target));
            
            #line 346 "..\..\..\MainWindow.xaml"
            this.GlobalKeybind.PreviewKeyDown += new System.Windows.Input.KeyEventHandler(this.Keybind_PreviewKeyDown);
            
            #line default
            #line hidden
            return;
            case 33:
            this.ThemeComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            case 34:
            this.AutoInjectCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 35:
            this.MinimizeToTrayCheckBox = ((System.Windows.Controls.CheckBox)(target));
            return;
            case 36:
            this.ProfileComboBox = ((System.Windows.Controls.ComboBox)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

