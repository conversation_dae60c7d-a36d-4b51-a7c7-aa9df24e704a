<Window x:Class="MinecraftPvPHack.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MinecraftPvPHack"
        mc:Ignorable="d"
        Title="Minecraft PvP Hack" Height="600" Width="900"
        WindowStyle="None" AllowsTransparency="True" Background="Transparent"
        ResizeMode="CanResize" WindowStartupLocation="CenterScreen">

    <Window.Resources>
        <!-- Dark Theme Colors -->
        <SolidColorBrush x:Key="BackgroundColor" Color="#FF2D2D2D"/>
        <SolidColorBrush x:Key="SidebarColor" Color="#FF3A3A3A"/>
        <SolidColorBrush x:Key="CardColor" Color="#FF404040"/>
        <SolidColorBrush x:Key="TextPrimary" Color="#FFEFEFEF"/>
        <SolidColorBrush x:Key="TextSecondary" Color="#FF9E9E9E"/>
        <SolidColorBrush x:Key="TextMuted" Color="#FF6E6E6E"/>
        <SolidColorBrush x:Key="BorderColor" Color="#FF555555"/>
        <SolidColorBrush x:Key="HoverColor" Color="#FF484848"/>
        <SolidColorBrush x:Key="AccentBlue" Color="#FF007AFF"/>
        <SolidColorBrush x:Key="AccentGreen" Color="#FF28A745"/>
        <SolidColorBrush x:Key="AccentRed" Color="#FFDC3545"/>

        <!-- Logo Gradient -->
        <LinearGradientBrush x:Key="LogoGradient" StartPoint="0,0" EndPoint="1,1">
            <GradientStop Color="#FF00D4FF" Offset="0"/>
            <GradientStop Color="#FF9C27B0" Offset="1"/>
        </LinearGradientBrush>

        <!-- Sidebar Item Style -->
        <Style x:Key="SidebarItem" TargetType="Border">
            <Setter Property="Padding" Value="15,12"/>
            <Setter Property="Margin" Value="0,1"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Active Sidebar Item Style -->
        <Style x:Key="SidebarItemActive" TargetType="Border" BasedOn="{StaticResource SidebarItem}">
            <Setter Property="Background" Value="{StaticResource HoverColor}"/>
        </Style>

        <!-- Modern Checkbox Style -->
        <Style x:Key="ModernCheckBox" TargetType="CheckBox">
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="CheckBox">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="CheckBoxBorder" Grid.Column="0"
                                    Width="20" Height="20"
                                    CornerRadius="6"
                                    BorderThickness="2"
                                    BorderBrush="{StaticResource BorderColor}"
                                    Background="{StaticResource CardColor}"
                                    Margin="0,0,12,0">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.2"/>
                                </Border.Effect>
                                <Path x:Name="CheckMark"
                                      Data="M 2 5 L 5 8 L 10 3"
                                      Stroke="White"
                                      StrokeThickness="2.5"
                                      Visibility="Collapsed"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"/>
                            </Border>
                            <ContentPresenter Grid.Column="1"
                                              VerticalAlignment="Center"
                                              Content="{TemplateBinding Content}"/>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsChecked" Value="True">
                                <Setter TargetName="CheckBoxBorder" Property="Background" Value="{StaticResource AccentBlue}"/>
                                <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{StaticResource AccentBlue}"/>
                                <Setter TargetName="CheckMark" Property="Visibility" Value="Visible"/>
                                <Setter TargetName="CheckBoxBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#FF007AFF" Direction="270" ShadowDepth="0" BlurRadius="8" Opacity="0.6"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="CheckBoxBorder" Property="BorderBrush" Value="{StaticResource AccentBlue}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern Slider Style -->
        <Style x:Key="ModernSlider" TargetType="Slider">
            <Setter Property="Height" Value="24"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Slider">
                        <Grid>
                            <Border x:Name="TrackBackground"
                                    Height="6"
                                    Background="{StaticResource BorderColor}"
                                    CornerRadius="3"
                                    VerticalAlignment="Center">
                                <Border.Effect>
                                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="2" Opacity="0.2"/>
                                </Border.Effect>
                            </Border>
                            <Border x:Name="PART_SelectionRange"
                                    Height="6"
                                    Background="{StaticResource AccentBlue}"
                                    CornerRadius="3"
                                    HorizontalAlignment="Left"
                                    VerticalAlignment="Center">
                                <Border.Effect>
                                    <DropShadowEffect Color="#FF007AFF" Direction="270" ShadowDepth="0" BlurRadius="6" Opacity="0.5"/>
                                </Border.Effect>
                            </Border>
                            <Thumb x:Name="PART_Track"
                                   Width="20"
                                   Height="20"
                                   Background="White"
                                   BorderBrush="{StaticResource AccentBlue}"
                                   BorderThickness="3"
                                   Template="{DynamicResource SliderThumbTemplate}"/>
                        </Grid>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Slider Thumb Template -->
        <ControlTemplate x:Key="SliderThumbTemplate" TargetType="Thumb">
            <Ellipse Fill="{TemplateBinding Background}"
                     Stroke="{TemplateBinding BorderBrush}"
                     StrokeThickness="{TemplateBinding BorderThickness}"
                     Width="{TemplateBinding Width}"
                     Height="{TemplateBinding Height}">
                <Ellipse.Effect>
                    <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="4" Opacity="0.3"/>
                </Ellipse.Effect>
            </Ellipse>
        </ControlTemplate>

        <!-- Modern Button Style -->
        <Style x:Key="ModernButton" TargetType="Button">
            <Setter Property="Background" Value="{StaticResource AccentBlue}"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,10"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border x:Name="ButtonBorder"
                                Background="{TemplateBinding Background}"
                                CornerRadius="8"
                                Padding="{TemplateBinding Padding}">
                            <Border.Effect>
                                <DropShadowEffect Color="Black" Direction="270" ShadowDepth="2" BlurRadius="6" Opacity="0.2"/>
                            </Border.Effect>
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="ButtonBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="#FF007AFF" Direction="270" ShadowDepth="0" BlurRadius="12" Opacity="0.6"/>
                                    </Setter.Value>
                                </Setter>
                                <Setter Property="Opacity" Value="0.95"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.85"/>
                                <Setter TargetName="ButtonBorder" Property="Effect">
                                    <Setter.Value>
                                        <DropShadowEffect Color="Black" Direction="270" ShadowDepth="1" BlurRadius="3" Opacity="0.3"/>
                                    </Setter.Value>
                                </Setter>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- KeyBind Button Style -->
        <Style x:Key="KeyBindButton" TargetType="Border">
            <Setter Property="Background" Value="{StaticResource CardColor}"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Padding" Value="8,6"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{StaticResource AccentBlue}"/>
                    <Setter Property="Background" Value="{StaticResource HoverColor}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Color Picker Style -->
        <Style x:Key="ColorPickerButton" TargetType="Border">
            <Setter Property="CornerRadius" Value="4"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="BorderThickness" Value="2"/>
            <Setter Property="BorderBrush" Value="{StaticResource BorderColor}"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="BorderBrush" Value="{StaticResource AccentBlue}"/>
                    <Setter Property="Effect">
                        <Setter.Value>
                            <DropShadowEffect Color="#FF007AFF" Direction="270" ShadowDepth="0" BlurRadius="6" Opacity="0.4"/>
                        </Setter.Value>
                    </Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <!-- Main Window Container -->
    <Border Background="{StaticResource BackgroundColor}" CornerRadius="12">
        <Border.Effect>
            <DropShadowEffect Color="Black" Direction="270" ShadowDepth="8" BlurRadius="20" Opacity="0.3"/>
        </Border.Effect>

        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>  <!-- Sidebar -->
                <ColumnDefinition Width="*"/>    <!-- Content -->
            </Grid.ColumnDefinitions>

            <!-- Left Sidebar -->
            <Border Grid.Column="0" Background="{StaticResource SidebarColor}" CornerRadius="12,0,0,12">
                <StackPanel Margin="0,20">
                    <!-- Sidebar Items -->
                    <Border x:Name="AimbotItem" Style="{StaticResource SidebarItemActive}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="Aimbot">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <!-- Active Indicator -->
                            <Border x:Name="AimbotIndicator" Grid.Column="0"
                                    Background="{StaticResource AccentBlue}"
                                    CornerRadius="0,2,2,0"/>
                            <!-- Icon -->
                            <Path Grid.Column="1"
                                  Data="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21A7,7 0 0,1 14,26H13V21.27C13.6,21.61 14,22.26 14,23A2,2 0 0,1 12,25A2,2 0 0,1 10,23C10,22.26 10.4,21.61 11,21.27V7.73C10.4,7.39 10,6.74 10,6A2,2 0 0,1 12,4A2,2 0 0,1 12,2Z"
                                  Fill="{StaticResource AccentBlue}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <!-- Text -->
                            <TextBlock Grid.Column="2" Text="Aimbot"
                                       Foreground="{StaticResource AccentBlue}"
                                       FontSize="13" FontWeight="Medium"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>

                    <Border x:Name="VisualsItem" Style="{StaticResource SidebarItem}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="Visuals">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="VisualsIndicator" Grid.Column="0" Background="Transparent"/>
                            <Path Grid.Column="1"
                                  Data="M12,9A3,3 0 0,0 9,12A3,3 0 0,0 12,15A3,3 0 0,0 15,12A3,3 0 0,0 12,9M12,17A5,5 0 0,1 7,12A5,5 0 0,1 12,7A5,5 0 0,1 17,12A5,5 0 0,1 12,17M12,4.5C7,4.5 2.73,7.61 1,12C2.73,16.39 7,19.5 12,19.5C17,19.5 21.27,16.39 23,12C21.27,7.61 17,4.5 12,4.5Z"
                                  Fill="{StaticResource TextSecondary}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="Visuals"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="13"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>

                    <!-- More Sidebar Items -->
                    <Border x:Name="TriggerItem" Style="{StaticResource SidebarItem}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="Trigger">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="TriggerIndicator" Grid.Column="0" Background="Transparent"/>
                            <Path Grid.Column="1"
                                  Data="M12,2A2,2 0 0,1 14,4C14,4.74 13.6,5.39 13,5.73V7H14A7,7 0 0,1 21,14H22A1,1 0 0,1 23,15V18A1,1 0 0,1 22,19H21A7,7 0 0,1 14,26H13V21.27C13.6,21.61 14,22.26 14,23A2,2 0 0,1 12,25A2,2 0 0,1 10,23C10,22.26 10.4,21.61 11,21.27V7.73C10.4,7.39 10,6.74 10,6A2,2 0 0,1 12,4A2,2 0 0,1 12,2Z"
                                  Fill="{StaticResource TextSecondary}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="Trigger"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="13"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>

                    <Border x:Name="UserItem" Style="{StaticResource SidebarItem}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="User">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="UserIndicator" Grid.Column="0" Background="Transparent"/>
                            <Path Grid.Column="1"
                                  Data="M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z"
                                  Fill="{StaticResource TextSecondary}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="User"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="13"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>

                    <Border x:Name="PoolsItem" Style="{StaticResource SidebarItem}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="Pools">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="PoolsIndicator" Grid.Column="0" Background="Transparent"/>
                            <Path Grid.Column="1"
                                  Data="M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"
                                  Fill="{StaticResource TextSecondary}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="Pools"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="13"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>

                    <Border x:Name="WorldItem" Style="{StaticResource SidebarItem}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="World">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="WorldIndicator" Grid.Column="0" Background="Transparent"/>
                            <Path Grid.Column="1"
                                  Data="M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z"
                                  Fill="{StaticResource TextSecondary}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="World"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="13"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>

                    <Border x:Name="MiscItem" Style="{StaticResource SidebarItem}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="Misc">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="MiscIndicator" Grid.Column="0" Background="Transparent"/>
                            <Path Grid.Column="1"
                                  Data="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.68 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"
                                  Fill="{StaticResource TextSecondary}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="Misc"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="13"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>

                    <Border x:Name="ScriptItem" Style="{StaticResource SidebarItem}"
                            MouseLeftButtonDown="SidebarItem_Click" Tag="Script">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="4"/>
                                <ColumnDefinition Width="20"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Border x:Name="ScriptIndicator" Grid.Column="0" Background="Transparent"/>
                            <Path Grid.Column="1"
                                  Data="M14.6,16.6L19.2,12L14.6,7.4L16,6L22,12L16,18L14.6,16.6M9.4,16.6L4.8,12L9.4,7.4L8,6L2,12L8,18L9.4,16.6Z"
                                  Fill="{StaticResource TextSecondary}"
                                  Width="14" Height="14"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            <TextBlock Grid.Column="2" Text="Script"
                                       Foreground="{StaticResource TextSecondary}"
                                       FontSize="13"
                                       VerticalAlignment="Center" Margin="8,0,0,0"/>
                        </Grid>
                    </Border>
                </StackPanel>
            </Border>

            <!-- Main Content Area -->
            <Grid Grid.Column="1">
                <Grid.RowDefinitions>
                    <RowDefinition Height="60"/>   <!-- Header -->
                    <RowDefinition Height="*"/>    <!-- Content -->
                </Grid.RowDefinitions>

                <!-- Top Header Bar -->
                <Border Grid.Row="0" Background="{StaticResource BackgroundColor}" Padding="20,15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Logo -->
                        <Border Grid.Column="0" Width="32" Height="32" CornerRadius="6" Margin="0,0,20,0">
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                    <GradientStop Color="#FF00D4FF" Offset="0"/>
                                    <GradientStop Color="#FF9C27B0" Offset="1"/>
                                </LinearGradientBrush>
                            </Border.Background>
                            <Path Data="M12,2L13.09,8.26L22,9L13.09,9.74L12,16L10.91,9.74L2,9L10.91,8.26L12,2Z"
                                  Fill="White" Width="16" Height="16"
                                  HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>

                        <!-- Sub-tabs -->
                        <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                            <Border x:Name="SubtabOne" Padding="12,8" Margin="0,0,20,0" Cursor="Hand"
                                    MouseLeftButtonDown="Subtab_Click" Tag="SubtabOne">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Setter Property="BorderThickness" Value="0,0,0,2"/>
                                        <Setter Property="BorderBrush" Value="{StaticResource AccentBlue}"/>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="Subtab One" Foreground="{StaticResource AccentBlue}"
                                           FontSize="13" FontWeight="Medium"/>
                            </Border>
                            <Border x:Name="SubtabTwo" Padding="12,8" Margin="0,0,20,0" Cursor="Hand"
                                    MouseLeftButtonDown="Subtab_Click" Tag="SubtabTwo">
                                <TextBlock Text="Subtab Two" Foreground="{StaticResource TextSecondary}"
                                           FontSize="13"/>
                            </Border>
                            <Border x:Name="SubtabThree" Padding="12,8" Cursor="Hand"
                                    MouseLeftButtonDown="Subtab_Click" Tag="SubtabThree">
                                <TextBlock Text="Subtab Three" Foreground="{StaticResource TextSecondary}"
                                           FontSize="13"/>
                            </Border>
                        </StackPanel>

                        <!-- Info Icon -->
                        <Border Grid.Column="2" Width="24" Height="24" CornerRadius="12"
                                Background="{StaticResource AccentBlue}" Cursor="Hand"
                                MouseLeftButtonDown="InfoButton_Click">
                            <TextBlock Text="i" Foreground="White" FontSize="12" FontWeight="Bold"
                                       HorizontalAlignment="Center" VerticalAlignment="Center"/>
                        </Border>
                    </Grid>
                </Border>

                <!-- Main Content Area -->
                <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Padding="20">
                    <Grid x:Name="ContentGrid">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="20"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <!-- General Section -->
                        <StackPanel x:Name="GeneralSection" Grid.Column="0">
                            <TextBlock Text="General" Foreground="{StaticResource TextPrimary}"
                                       FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                            <!-- Godmode -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <CheckBox Grid.Column="0" Content="Godmode"
                                          Style="{StaticResource ModernCheckBox}"
                                          Foreground="{StaticResource TextPrimary}" FontSize="13"/>
                                <Border Grid.Column="1" Style="{StaticResource KeyBindButton}"
                                        Margin="8,0" MouseLeftButtonDown="KeyBind_Click" Tag="Godmode">
                                    <Path Data="M20,5H4C2.89,5 2,5.89 2,7V17C2,18.11 2.89,19 4,19H20C21.11,19 22,18.11 22,17V7C22,5.89 21.11,5 20,5M20,17H4V7H20V17Z"
                                          Fill="{StaticResource TextSecondary}" Width="12" Height="12"/>
                                </Border>
                                <TextBlock Grid.Column="2" x:Name="GodmodeKey" Text="LMouse"
                                           Foreground="{StaticResource TextSecondary}" FontSize="11"
                                           VerticalAlignment="Center"/>
                            </Grid>

                            <!-- No ragdoll -->
                            <CheckBox Content="No ragdoll" IsChecked="True"
                                      Style="{StaticResource ModernCheckBox}"
                                      Foreground="{StaticResource TextPrimary}" FontSize="13"
                                      Margin="0,0,0,12"/>

                            <!-- Suicide Button -->
                            <Button Content="Suicide" Style="{StaticResource ModernButton}"
                                    HorizontalAlignment="Left" Margin="0,8,0,0"
                                    Click="SuicideButton_Click"/>
                        </StackPanel>

                        <!-- Movements Section -->
                        <StackPanel x:Name="MovementsSection" Grid.Column="2">
                            <TextBlock Text="Movements" Foreground="{StaticResource TextPrimary}"
                                       FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                            <!-- Speed -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <CheckBox Grid.Column="0" Content="Speed"
                                          Style="{StaticResource ModernCheckBox}"
                                          Foreground="{StaticResource TextPrimary}" FontSize="13"/>
                                <Border Grid.Column="1" Style="{StaticResource KeyBindButton}"
                                        Margin="8,0" MouseLeftButtonDown="KeyBind_Click" Tag="Speed">
                                    <Path Data="M20,5H4C2.89,5 2,5.89 2,7V17C2,18.11 2.89,19 4,19H20C21.11,19 22,18.11 22,17V7C22,5.89 21.11,5 20,5M20,17H4V7H20V17Z"
                                          Fill="{StaticResource TextSecondary}" Width="12" Height="12"/>
                                </Border>
                                <TextBlock Grid.Column="2" x:Name="SpeedKey" Text="Space"
                                           Foreground="{StaticResource TextSecondary}" FontSize="11"
                                           VerticalAlignment="Center"/>
                            </Grid>

                            <!-- Fly -->
                            <CheckBox Content="Fly"
                                      Style="{StaticResource ModernCheckBox}"
                                      Foreground="{StaticResource TextPrimary}" FontSize="13"
                                      Margin="0,0,0,12"/>

                            <!-- Jump -->
                            <CheckBox Content="Jump"
                                      Style="{StaticResource ModernCheckBox}"
                                      Foreground="{StaticResource TextPrimary}" FontSize="13"
                                      Margin="0,0,0,12"/>
                        </StackPanel>

                        <!-- Health Section -->
                        <StackPanel x:Name="HealthSection" Grid.Column="4">
                            <TextBlock Text="Health" Foreground="{StaticResource TextPrimary}"
                                       FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                            <!-- Health value slider -->
                            <Grid Margin="0,0,0,15">
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto"/>
                                </Grid.RowDefinitions>
                                <Grid Grid.Row="0" Margin="0,0,0,8">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>
                                    <TextBlock Grid.Column="0" Text="Health value"
                                               Foreground="{StaticResource TextPrimary}" FontSize="13"/>
                                    <TextBlock Grid.Column="1" x:Name="HealthValue" Text="100"
                                               Foreground="{StaticResource TextPrimary}" FontSize="13"/>
                                </Grid>
                                <Slider Grid.Row="1" x:Name="HealthSlider"
                                        Style="{StaticResource ModernSlider}"
                                        Minimum="1" Maximum="100" Value="100"
                                        ValueChanged="Slider_ValueChanged"/>
                            </Grid>
                        </StackPanel>


                        <!-- Esp Section -->
                        <StackPanel x:Name="EspSection" Grid.Column="6">
                            <TextBlock Text="Esp" Foreground="{StaticResource TextPrimary}"
                                       FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>

                            <!-- Player ESP -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <CheckBox Grid.Column="0" Content="Player"
                                          Style="{StaticResource ModernCheckBox}"
                                          Foreground="{StaticResource TextPrimary}" FontSize="13"/>
                                <Border Grid.Column="1" Width="16" Height="16"
                                        Background="#FFFF0000" CornerRadius="2"
                                        Cursor="Hand" MouseLeftButtonDown="ColorPicker_Click" Tag="PlayerColor"/>
                            </Grid>

                            <!-- Item ESP -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <CheckBox Grid.Column="0" Content="Item"
                                          Style="{StaticResource ModernCheckBox}"
                                          Foreground="{StaticResource TextPrimary}" FontSize="13"/>
                                <Border Grid.Column="1" Width="16" Height="16"
                                        Background="#FF00FF00" CornerRadius="2"
                                        Cursor="Hand" MouseLeftButtonDown="ColorPicker_Click" Tag="ItemColor"/>
                            </Grid>

                            <!-- Mob ESP -->
                            <Grid Margin="0,0,0,12">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>
                                <CheckBox Grid.Column="0" Content="Mob"
                                          Style="{StaticResource ModernCheckBox}"
                                          Foreground="{StaticResource TextPrimary}" FontSize="13"/>
                                <Border Grid.Column="1" Width="16" Height="16"
                                        Background="#FF0000FF" CornerRadius="2"
                                        Cursor="Hand" MouseLeftButtonDown="ColorPicker_Click" Tag="MobColor"/>
                            </Grid>
                        </StackPanel>
                    </Grid>
                </ScrollViewer>
            </Grid>
        </Grid>
    </Border>
</Window>


