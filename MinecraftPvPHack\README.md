# 🎮 Minecraft PvP Hack - Interface Profissional

Uma interface moderna e profissional para hack PvP do Minecraft 1.8.9, desenvolvida em WPF com Visual Studio 2022.

## ✨ Características

### 🎨 Design Moderno
- Interface dark theme profissional
- Animações suaves e efeitos visuais
- Layout responsivo com 3 painéis
- Ícones e emojis para melhor UX

### ⚔️ Módulos de Combat
- **KillAura**: Ataque automático com configurações de range e APS
- **Velocity**: Redução de knockback (horizontal/vertical)
- **Criticals**: Ataques críticos automáticos
- **AutoClicker**: Clique automático configurável
- **Reach**: Extensão do alcance de ataque

### 👁️ Módulos Visuais
- **ESP**: Visualização de players através de paredes
- **Fullbright**: Iluminação máxima
- **X-Ray**: Visualização de minérios
- **NoFall**: Prevenção de dano de queda

### ⚙️ Configurações Avançadas
- Sistema de profiles (Default, PvP Aggressive, Legit)
- Keybinds personalizáveis
- Temas de cores
- Auto-inject
- Minimizar para bandeja

## 🚀 Como Usar

### 1. Compilar o Projeto
```bash
cd MinecraftPvPHack
dotnet build
```

### 2. Executar a Interface
```bash
dotnet run
```

### 3. Usar o Hack
1. Abra o Minecraft 1.8.9
2. Aguarde a detecção automática (status ficará verde)
3. Clique em "🚀 INJETAR" para ativar o hack
4. Configure os módulos conforme necessário
5. Use "💾 SALVAR" para salvar suas configurações

## 🔧 Estrutura do Projeto

```
MinecraftPvPHack/
├── MainWindow.xaml          # Interface visual (XAML)
├── MainWindow.xaml.cs       # Lógica da interface (C#)
├── App.xaml                 # Configurações da aplicação
├── App.xaml.cs              # Inicialização da aplicação
└── MinecraftPvPHack.csproj  # Arquivo do projeto
```

## 🎯 Próximos Passos para Integração

### 1. Implementar Injeção Real
- Adicionar biblioteca de injeção de DLL
- Implementar detecção do processo do Minecraft
- Criar sistema de hooks para as funções do jogo

### 2. Adicionar Funcionalidades dos Módulos
- Implementar lógica real do KillAura
- Adicionar sistema de packets para Velocity
- Criar overlay para ESP e outros visuais

### 3. Sistema de Configuração
- Salvar/carregar configurações em arquivo JSON
- Sistema de profiles mais avançado
- Backup automático de configurações

## 🛠️ Tecnologias Utilizadas

- **Framework**: .NET 9.0
- **UI**: WPF (Windows Presentation Foundation)
- **IDE**: Visual Studio 2022
- **Linguagem**: C#

## ⚠️ Aviso Legal

Este projeto é apenas para fins educacionais e de demonstração. O uso de hacks em servidores públicos pode resultar em banimento. Use apenas em servidores próprios ou com permissão.

## 🎨 Personalização

### Alterar Cores do Tema
Edite as cores no arquivo `MainWindow.xaml`:
```xml
<SolidColorBrush x:Key="PrimaryColor" Color="#FF1E1E1E"/>
<SolidColorBrush x:Key="AccentColor" Color="#FF007ACC"/>
```

### Adicionar Novos Módulos
1. Adicione o ToggleButton no XAML
2. Implemente o handler no código-behind
3. Adicione as configurações específicas do módulo

### Modificar Layout
O layout usa Grid com 3 colunas:
- Coluna 0: Módulos de Combat
- Coluna 1: Status e Log
- Coluna 2: Módulos Visuais e Configurações

## 📞 Suporte

Para dúvidas sobre implementação ou personalização, consulte a documentação do WPF ou entre em contato.

---
**Desenvolvido com ❤️ para a comunidade Minecraft PvP**
