using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace MinecraftPvPHack;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer statusTimer;
    private Dictionary<string, bool> moduleStates;
    private bool isInjected = false;
    private Random random = new Random();

    public MainWindow()
    {
        InitializeComponent();
        InitializeHack();
    }

    private void InitializeHack()
    {
        // Inicializar estados dos módulos
        moduleStates = new Dictionary<string, bool>();

        // Configurar timer para atualizar status
        statusTimer = new DispatcherTimer();
        statusTimer.Interval = TimeSpan.FromMilliseconds(500);
        statusTimer.Tick += StatusTimer_Tick;
        statusTimer.Start();

        // Log inicial
        AddLog("[INFO] Hack iniciado");
        AddLog("[INFO] Aguardando Minecraft...");
        AddLog("[INFO] Interface carregada com sucesso");

        // Simular detecção do Minecraft após alguns segundos
        Task.Delay(3000).ContinueWith(_ =>
        {
            Dispatcher.Invoke(() =>
            {
                MinecraftStatus.Text = "🟢 Detectado";
                MinecraftStatus.Foreground = (SolidColorBrush)FindResource("SuccessColor");
                AddLog("[SUCCESS] Minecraft 1.8.9 detectado!");
            });
        });
    }

    private void StatusTimer_Tick(object sender, EventArgs e)
    {
        // Simular FPS e Ping
        if (isInjected)
        {
            FPSCounter.Text = (random.Next(60, 120)).ToString();
            PingCounter.Text = (random.Next(20, 80)).ToString() + "ms";
        }

        // Atualizar contador de módulos ativos
        int activeCount = 0;
        foreach (var state in moduleStates.Values)
        {
            if (state) activeCount++;
        }
        ActiveModules.Text = activeCount.ToString();
    }

    private void AddLog(string message)
    {
        string timestamp = DateTime.Now.ToString("HH:mm:ss");
        LogTextBlock.Text += $"\n[{timestamp}] {message}";
        LogScrollViewer.ScrollToEnd();
    }

    // Event handlers para controles da janela
    private void Header_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ButtonState == MouseButtonState.Pressed)
            this.DragMove();
    }

    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        this.WindowState = WindowState.Minimized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        this.Close();
    }

    // Event handlers para módulos
    private void ModuleToggle_Click(object sender, RoutedEventArgs e)
    {
        if (sender is ToggleButton toggle)
        {
            string moduleName = toggle.Content.ToString();
            bool isEnabled = toggle.IsChecked ?? false;

            moduleStates[moduleName] = isEnabled;

            if (isEnabled)
            {
                AddLog($"[MODULE] {moduleName} ativado");
                ShowModuleSettings(moduleName, true);
            }
            else
            {
                AddLog($"[MODULE] {moduleName} desativado");
                ShowModuleSettings(moduleName, false);
            }
        }
    }

    private void ShowModuleSettings(string moduleName, bool show)
    {
        var visibility = show ? Visibility.Visible : Visibility.Collapsed;

        switch (moduleName)
        {
            case "KillAura":
                KillAuraSettings.Visibility = visibility;
                break;
            case "Velocity":
                VelocitySettings.Visibility = visibility;
                break;
            case "AutoClicker":
                AutoClickerSettings.Visibility = visibility;
                break;
            case "Reach":
                ReachSettings.Visibility = visibility;
                break;
        }
    }

    // Event handlers para botões principais
    private void InjectButton_Click(object sender, RoutedEventArgs e)
    {
        if (!isInjected)
        {
            isInjected = true;
            InjectButton.Content = "✅ INJETADO";
            InjectButton.IsEnabled = false;
            EjectButton.IsEnabled = true;
            AddLog("[SUCCESS] Hack injetado com sucesso!");
            AddLog("[INFO] Todos os módulos estão prontos para uso");
        }
    }

    private void EjectButton_Click(object sender, RoutedEventArgs e)
    {
        if (isInjected)
        {
            isInjected = false;
            InjectButton.Content = "🚀 INJETAR";
            InjectButton.IsEnabled = true;
            EjectButton.IsEnabled = false;

            // Desativar todos os módulos
            foreach (var control in FindVisualChildren<ToggleButton>(this))
            {
                control.IsChecked = false;
                ModuleToggle_Click(control, new RoutedEventArgs());
            }

            AddLog("[INFO] Hack ejetado com sucesso!");
            FPSCounter.Text = "--";
            PingCounter.Text = "--ms";
        }
    }

    private void SaveConfigButton_Click(object sender, RoutedEventArgs e)
    {
        AddLog("[INFO] Configuração salva!");
        // Aqui você implementaria a lógica de salvar configurações
    }

    // Event handler para keybinds
    private void Keybind_PreviewKeyDown(object sender, KeyEventArgs e)
    {
        if (sender is TextBox textBox)
        {
            textBox.Text = e.Key.ToString();
            e.Handled = true;
            AddLog($"[CONFIG] Keybind alterado para: {e.Key}");
        }
    }

    // Método auxiliar para encontrar controles filhos
    private static IEnumerable<T> FindVisualChildren<T>(DependencyObject depObj) where T : DependencyObject
    {
        if (depObj != null)
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(depObj); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(depObj, i);
                if (child != null && child is T)
                {
                    yield return (T)child;
                }

                foreach (T childOfChild in FindVisualChildren<T>(child))
                {
                    yield return childOfChild;
                }
            }
        }
    }
}