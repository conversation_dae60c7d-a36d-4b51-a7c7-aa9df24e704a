using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Threading;

namespace MinecraftPvPHack;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private DispatcherTimer statusTimer;
    private Dictionary<string, bool> moduleStates;
    private bool isInjected = false;
    private Random random = new Random();
    private string currentSection = "Combat";

    public MainWindow()
    {
        InitializeComponent();
        InitializeHack();
    }

    private void InitializeHack()
    {
        // Inicializar estados dos módulos
        moduleStates = new Dictionary<string, bool>();

        // Configurar timer para atualizar status
        statusTimer = new DispatcherTimer();
        statusTimer.Interval = TimeSpan.FromMilliseconds(1000);
        statusTimer.Tick += StatusTimer_Tick;
        statusTimer.Start();

        // Simular detecção do Minecraft após alguns segundos
        Task.Delay(2000).ContinueWith(_ =>
        {
            Dispatcher.Invoke(() =>
            {
                MinecraftStatusText.Text = "🟢 Connected";
                MinecraftStatusText.Foreground = (SolidColorBrush)FindResource("AccentColor");
            });
        });
    }

    private void StatusTimer_Tick(object sender, EventArgs e)
    {
        // Atualizar contador de módulos ativos
        int activeCount = 0;
        foreach (var state in moduleStates.Values)
        {
            if (state) activeCount++;
        }
        ActiveModulesText.Text = activeCount.ToString();
    }

    // Event handlers para controles da janela
    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        this.WindowState = WindowState.Minimized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        this.Close();
    }

    // Event handlers para sidebar
    private void SidebarButton_Click(object sender, RoutedEventArgs e)
    {
        if (sender is Button button && button.Tag is string section)
        {
            // Resetar estilos de todos os botões
            ResetSidebarButtons();

            // Ativar botão atual
            button.Style = (Style)FindResource("SidebarButtonActive");

            // Atualizar seção atual
            currentSection = section;
            SectionTitle.Text = $"World > {section}";

            // Aqui você pode adicionar lógica para mostrar diferentes conteúdos
            // baseado na seção selecionada
        }
    }

    private void ResetSidebarButtons()
    {
        var sidebarButtonStyle = (Style)FindResource("SidebarButton");
        CombatBtn.Style = sidebarButtonStyle;
        MovementBtn.Style = sidebarButtonStyle;
        VisualBtn.Style = sidebarButtonStyle;
        WorldBtn.Style = sidebarButtonStyle;
        PlayerBtn.Style = sidebarButtonStyle;
        MiscBtn.Style = sidebarButtonStyle;
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        // Implementar janela de configurações
        MessageBox.Show("Configurações em desenvolvimento!", "Info", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    // Event handlers para módulos
    private void ModuleItem_Click(object sender, MouseButtonEventArgs e)
    {
        if (sender is Border border && border.Tag is string moduleName)
        {
            // Toggle do módulo
            bool currentState = moduleStates.ContainsKey(moduleName) ? moduleStates[moduleName] : false;
            moduleStates[moduleName] = !currentState;

            // Atualizar visual do módulo
            if (moduleStates[moduleName])
            {
                border.Background = (SolidColorBrush)FindResource("AccentColor");
            }
            else
            {
                border.Background = Brushes.Transparent;
            }
        }
    }

    // Event handlers para sliders
    private void Slider_ValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (sender is Slider slider)
        {
            if (slider.Name == "KillAuraRangeSlider" && KillAuraRangeValue != null)
            {
                KillAuraRangeValue.Text = slider.Value.ToString("F2");
            }
            else if (slider.Name == "AutoClickerCPSSlider" && AutoClickerCPSValue != null)
            {
                AutoClickerCPSValue.Text = slider.Value.ToString("F0");
            }
            else if (slider.Name == "VelocityHSlider" && VelocityHValue != null)
            {
                VelocityHValue.Text = slider.Value.ToString("F0") + "%";
            }
        }
    }
}