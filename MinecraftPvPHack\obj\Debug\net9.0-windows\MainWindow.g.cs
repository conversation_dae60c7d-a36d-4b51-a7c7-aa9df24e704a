﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1760B729D27F51727856D4AE5579128152BCF31F"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MinecraftPvPHack;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MinecraftPvPHack {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 190 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border CombatIcon;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MovementIcon;
        
        #line default
        #line hidden
        
        
        #line 204 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border VisualIcon;
        
        #line default
        #line hidden
        
        
        #line 211 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WorldIcon;
        
        #line default
        #line hidden
        
        
        #line 218 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PlayerIcon;
        
        #line default
        #line hidden
        
        
        #line 225 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MiscIcon;
        
        #line default
        #line hidden
        
        
        #line 257 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SectionTitle;
        
        #line default
        #line hidden
        
        
        #line 286 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ContentGrid;
        
        #line default
        #line hidden
        
        
        #line 294 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CombatSection;
        
        #line default
        #line hidden
        
        
        #line 308 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border EnableSilentToggle;
        
        #line default
        #line hidden
        
        
        #line 310 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse EnableSilentThumb;
        
        #line default
        #line hidden
        
        
        #line 354 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UndetectableToggle;
        
        #line default
        #line hidden
        
        
        #line 365 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse UndetectableThumb;
        
        #line default
        #line hidden
        
        
        #line 388 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border VisibleCheckToggle;
        
        #line default
        #line hidden
        
        
        #line 399 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse VisibleCheckThumb;
        
        #line default
        #line hidden
        
        
        #line 411 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel AimbotSection;
        
        #line default
        #line hidden
        
        
        #line 428 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border EnableAimbotToggle;
        
        #line default
        #line hidden
        
        
        #line 430 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse EnableAimbotThumb;
        
        #line default
        #line hidden
        
        
        #line 458 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FOVValue;
        
        #line default
        #line hidden
        
        
        #line 462 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider FOVSlider;
        
        #line default
        #line hidden
        
        
        #line 486 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DistanceValue;
        
        #line default
        #line hidden
        
        
        #line 490 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider DistanceSlider;
        
        #line default
        #line hidden
        
        
        #line 514 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock XSmoothValue;
        
        #line default
        #line hidden
        
        
        #line 518 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider XSmoothSlider;
        
        #line default
        #line hidden
        
        
        #line 542 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock YSmoothValue;
        
        #line default
        #line hidden
        
        
        #line 546 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider YSmoothSlider;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MinecraftPvPHack;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.CombatIcon = ((System.Windows.Controls.Border)(target));
            
            #line 191 "..\..\..\MainWindow.xaml"
            this.CombatIcon.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarIcon_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.MovementIcon = ((System.Windows.Controls.Border)(target));
            
            #line 198 "..\..\..\MainWindow.xaml"
            this.MovementIcon.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarIcon_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.VisualIcon = ((System.Windows.Controls.Border)(target));
            
            #line 205 "..\..\..\MainWindow.xaml"
            this.VisualIcon.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarIcon_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.WorldIcon = ((System.Windows.Controls.Border)(target));
            
            #line 212 "..\..\..\MainWindow.xaml"
            this.WorldIcon.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarIcon_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.PlayerIcon = ((System.Windows.Controls.Border)(target));
            
            #line 219 "..\..\..\MainWindow.xaml"
            this.PlayerIcon.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarIcon_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.MiscIcon = ((System.Windows.Controls.Border)(target));
            
            #line 226 "..\..\..\MainWindow.xaml"
            this.MiscIcon.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarIcon_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.SectionTitle = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            
            #line 270 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.MinimizeButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 275 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.ContentGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 11:
            this.CombatSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 12:
            this.EnableSilentToggle = ((System.Windows.Controls.Border)(target));
            
            #line 309 "..\..\..\MainWindow.xaml"
            this.EnableSilentToggle.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Toggle_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.EnableSilentThumb = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 14:
            this.UndetectableToggle = ((System.Windows.Controls.Border)(target));
            
            #line 355 "..\..\..\MainWindow.xaml"
            this.UndetectableToggle.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Toggle_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.UndetectableThumb = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 16:
            this.VisibleCheckToggle = ((System.Windows.Controls.Border)(target));
            
            #line 389 "..\..\..\MainWindow.xaml"
            this.VisibleCheckToggle.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Toggle_Click);
            
            #line default
            #line hidden
            return;
            case 17:
            this.VisibleCheckThumb = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 18:
            this.AimbotSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 19:
            this.EnableAimbotToggle = ((System.Windows.Controls.Border)(target));
            
            #line 429 "..\..\..\MainWindow.xaml"
            this.EnableAimbotToggle.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Toggle_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            this.EnableAimbotThumb = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 21:
            this.FOVValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 22:
            this.FOVSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 464 "..\..\..\MainWindow.xaml"
            this.FOVSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.Slider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 23:
            this.DistanceValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 24:
            this.DistanceSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 492 "..\..\..\MainWindow.xaml"
            this.DistanceSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.Slider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 25:
            this.XSmoothValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 26:
            this.XSmoothSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 520 "..\..\..\MainWindow.xaml"
            this.XSmoothSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.Slider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 27:
            this.YSmoothValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 28:
            this.YSmoothSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 548 "..\..\..\MainWindow.xaml"
            this.YSmoothSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.Slider_ValueChanged);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

