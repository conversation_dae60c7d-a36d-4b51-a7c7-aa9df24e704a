﻿#pragma checksum "..\..\..\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F860CEAA7C1B730153D1838F3082A359C018D8F0"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MinecraftPvPHack;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace MinecraftPvPHack {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 171 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AimbotItem;
        
        #line default
        #line hidden
        
        
        #line 180 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AimbotIndicator;
        
        #line default
        #line hidden
        
        
        #line 197 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border VisualsItem;
        
        #line default
        #line hidden
        
        
        #line 205 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border VisualsIndicator;
        
        #line default
        #line hidden
        
        
        #line 219 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TriggerItem;
        
        #line default
        #line hidden
        
        
        #line 227 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border TriggerIndicator;
        
        #line default
        #line hidden
        
        
        #line 240 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UserItem;
        
        #line default
        #line hidden
        
        
        #line 248 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border UserIndicator;
        
        #line default
        #line hidden
        
        
        #line 261 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PoolsItem;
        
        #line default
        #line hidden
        
        
        #line 269 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border PoolsIndicator;
        
        #line default
        #line hidden
        
        
        #line 282 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WorldItem;
        
        #line default
        #line hidden
        
        
        #line 290 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border WorldIndicator;
        
        #line default
        #line hidden
        
        
        #line 303 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MiscItem;
        
        #line default
        #line hidden
        
        
        #line 311 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border MiscIndicator;
        
        #line default
        #line hidden
        
        
        #line 324 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ScriptItem;
        
        #line default
        #line hidden
        
        
        #line 332 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ScriptIndicator;
        
        #line default
        #line hidden
        
        
        #line 378 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SubtabOne;
        
        #line default
        #line hidden
        
        
        #line 389 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SubtabTwo;
        
        #line default
        #line hidden
        
        
        #line 394 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border SubtabThree;
        
        #line default
        #line hidden
        
        
        #line 413 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Grid ContentGrid;
        
        #line default
        #line hidden
        
        
        #line 425 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel GeneralSection;
        
        #line default
        #line hidden
        
        
        #line 445 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GodmodeKey;
        
        #line default
        #line hidden
        
        
        #line 463 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel MovementsSection;
        
        #line default
        #line hidden
        
        
        #line 483 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SpeedKey;
        
        #line default
        #line hidden
        
        
        #line 502 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel HealthSection;
        
        #line default
        #line hidden
        
        
        #line 519 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock HealthValue;
        
        #line default
        #line hidden
        
        
        #line 522 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider HealthSlider;
        
        #line default
        #line hidden
        
        
        #line 531 "..\..\..\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel EspSection;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/MinecraftPvPHack;component/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.6.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.AimbotItem = ((System.Windows.Controls.Border)(target));
            
            #line 172 "..\..\..\MainWindow.xaml"
            this.AimbotItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.AimbotIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.VisualsItem = ((System.Windows.Controls.Border)(target));
            
            #line 198 "..\..\..\MainWindow.xaml"
            this.VisualsItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.VisualsIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 5:
            this.TriggerItem = ((System.Windows.Controls.Border)(target));
            
            #line 220 "..\..\..\MainWindow.xaml"
            this.TriggerItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TriggerIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.UserItem = ((System.Windows.Controls.Border)(target));
            
            #line 241 "..\..\..\MainWindow.xaml"
            this.UserItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.UserIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 9:
            this.PoolsItem = ((System.Windows.Controls.Border)(target));
            
            #line 262 "..\..\..\MainWindow.xaml"
            this.PoolsItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.PoolsIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 11:
            this.WorldItem = ((System.Windows.Controls.Border)(target));
            
            #line 283 "..\..\..\MainWindow.xaml"
            this.WorldItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.WorldIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.MiscItem = ((System.Windows.Controls.Border)(target));
            
            #line 304 "..\..\..\MainWindow.xaml"
            this.MiscItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            this.MiscIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 15:
            this.ScriptItem = ((System.Windows.Controls.Border)(target));
            
            #line 325 "..\..\..\MainWindow.xaml"
            this.ScriptItem.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.SidebarItem_Click);
            
            #line default
            #line hidden
            return;
            case 16:
            this.ScriptIndicator = ((System.Windows.Controls.Border)(target));
            return;
            case 17:
            this.SubtabOne = ((System.Windows.Controls.Border)(target));
            
            #line 379 "..\..\..\MainWindow.xaml"
            this.SubtabOne.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Subtab_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.SubtabTwo = ((System.Windows.Controls.Border)(target));
            
            #line 390 "..\..\..\MainWindow.xaml"
            this.SubtabTwo.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Subtab_Click);
            
            #line default
            #line hidden
            return;
            case 19:
            this.SubtabThree = ((System.Windows.Controls.Border)(target));
            
            #line 395 "..\..\..\MainWindow.xaml"
            this.SubtabThree.MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.Subtab_Click);
            
            #line default
            #line hidden
            return;
            case 20:
            
            #line 404 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.InfoButton_Click);
            
            #line default
            #line hidden
            return;
            case 21:
            this.ContentGrid = ((System.Windows.Controls.Grid)(target));
            return;
            case 22:
            this.GeneralSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 23:
            
            #line 441 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.KeyBind_Click);
            
            #line default
            #line hidden
            return;
            case 24:
            this.GodmodeKey = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 25:
            
            #line 459 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SuicideButton_Click);
            
            #line default
            #line hidden
            return;
            case 26:
            this.MovementsSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 27:
            
            #line 479 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.KeyBind_Click);
            
            #line default
            #line hidden
            return;
            case 28:
            this.SpeedKey = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 29:
            this.HealthSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 30:
            this.HealthValue = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 31:
            this.HealthSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 525 "..\..\..\MainWindow.xaml"
            this.HealthSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.Slider_ValueChanged);
            
            #line default
            #line hidden
            return;
            case 32:
            this.EspSection = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 33:
            
            #line 546 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPicker_Click);
            
            #line default
            #line hidden
            return;
            case 34:
            
            #line 560 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPicker_Click);
            
            #line default
            #line hidden
            return;
            case 35:
            
            #line 574 "..\..\..\MainWindow.xaml"
            ((System.Windows.Controls.Border)(target)).MouseLeftButtonDown += new System.Windows.Input.MouseButtonEventHandler(this.ColorPicker_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

